{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "react-scripts start", "dev": "vite --port 3000", "build": "vite build", "preview": "vite preview --port 3000"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.25.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}