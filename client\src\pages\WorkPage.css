/* Carousel.css */
.auto-carousel {
  width: 60%;
  height: 80vh;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto; /* 关键：通过自动外边距实现水平居中 */
  border-radius: 12px; /* 可选：添加圆角效果 */
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2); /* 可选：添加阴影增强层次感 */
}

.carousel-track {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transform: scale(1.05);
  transition: all 0.8s ease-in-out;
  z-index: 1;
}

.carousel-slide.active {
  opacity: 1;
  transform: scale(1);
  z-index: 2;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 32px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: 30px;
}

.next-btn {
  right: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-carousel {
    height: 60vh;
  }

  .nav-btn {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .prev-btn {
    left: 15px;
  }

  .next-btn {
    right: 15px;
  }
}

@media (max-width: 480px) {
  .auto-carousel {
    height: 50vh;
  }

  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

.downline3 {
  color: #000;
  display: flex;
  flex-direction: column;
  font-size: 24px;
  font-family: "中易宋体", "SimSun", serif;
  font-weight: 700;
}
